"use client";

import React from "react";
import { useState, useCallback } from "react";
import { Stage, Layer, Rect } from "react-konva";
import Konva from "konva";
import { canvasStorage, type CanvasState } from "@/lib/storage";
// import { motion, AnimatePresence } from "framer-motion";
import { Button, buttonVariants } from "@/components/ui/button";
import { ImageIcon, LoaderCircleIcon, PlusIcon, ZapIcon, CoinsIcon, CreditCard, MailIcon, GemIcon, LogOutIcon, FolderIcon } from "lucide-react";
import { cn, sleep } from "@/lib/utils";
import { useRef, useEffect } from "react";
import { Textarea } from "@/components/ui/textarea";
import { TooltipProvider, TooltipTrigger, TooltipContent, Tooltip } from "@/components/ui/tooltip";
import { ContextMenu, ContextMenuTrigger } from "@/components/ui/context-menu";
import { createFalClient } from "@fal-ai/client";
import { DropdownMenu, DropdownMenuContent, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { useSession } from "@/lib/auth-client";
import { useUserStore } from "@/store/useUserStore";
import { useSignInBoxOpenStore } from "@/store/useSignInBoxOpenStore";
import { usePlanBoxOpenStore } from "@/store/usePlanBoxOpenStore";
import { useSignOut } from "@/hooks/use-signout";
import { MembershipID } from "@/@types/membership-type";
import { Separator } from "@/components/ui/separator";
import { EMAIL_CONTACT, WEBNAME } from "@/lib/constants";
import Link from "next/link";

// Import extracted components
import { HandleGenerateImage } from "@/components/canvas/HandleGenerateImage";
import { CropOverlayWrapper } from "@/components/canvas/CropOverlayWrapper";
import { CanvasImage } from "@/components/canvas/CanvasImage";
import { useMutation } from "@tanstack/react-query";

// Import types
import type { PlacedImage, GenerationSettings, ActiveGeneration, SelectionBox } from "@/@types/canvas";

import { imageToCanvasElement } from "@/lib/utils-canvas";
import { checkOS } from "@/lib/utils-os";

// Import additional extracted components
import { SelectionBoxComponent } from "@/components/canvas/SelectionBox";
import { ZoomControls } from "@/components/canvas/ZoomControls";
import { MobileToolbar } from "@/components/canvas/MobileToolbar";
import { HeaderDropdown } from "@/components/canvas/HeaderDropdown";
import { CanvasContextMenu } from "@/components/canvas/CanvasContextMenu";
import { DimensionDisplay } from "@/components/canvas/DimensionDisplay";

// Import handlers
import { handleRun as handleRunHandler, uploadImageDirect, generateImage } from "@/lib/handlers/generation-handler";
import { handleRemoveBackground as handleRemoveBackgroundHandler } from "@/lib/handlers/background-handler";
import { toast } from "sonner";
import { orpc } from "@/orpc/client";
import { uploadFile } from "@/lib/file/upload-file";
import { useImagesHistory } from "@/lib/hooks/useImagesHistory";

// import { GenerationsIndicator } from "@/components/generations-indicator";

export default function EditCanvas() {
	const { images, setImages, update, reset, undo, redo, canUndo, canRedo } = useImagesHistory([]);
	const [selectedIds, setSelectedIds] = useState<string[]>([]);
	const [isStorageLoaded, setIsStorageLoaded] = useState(false);
	const [visibleIndicators, setVisibleIndicators] = useState<Set<string>>(new Set());
	// const { toast } = useToast();

	// User authentication and store hooks
	const { data: session } = useSession();
	const { setSignInBoxOpen } = useSignInBoxOpenStore();
	const { setPlanBoxOpen } = usePlanBoxOpenStore();
	const { handleSignOut } = useSignOut();
	const { user, creditsAll: userCreditsAll, hasPaid: userHasPaid } = useUserStore();

	const [generationSettings, setGenerationSettings] = useState<GenerationSettings>({
		prompt: "",
	});

	const [isGenerating, setIsGenerating] = useState(false);
	const [activeGenerations, setActiveGenerations] = useState<Map<string, ActiveGeneration>>(new Map());
	const [selectionBox, setSelectionBox] = useState<SelectionBox>({
		startX: 0,
		startY: 0,
		endX: 0,
		endY: 0,
		visible: false,
	});
	const [isSelecting, setIsSelecting] = useState(false);
	const [dragStartPositions, setDragStartPositions] = useState<Map<string, { x: number; y: number }>>(new Map());
	const [isDraggingImage, setIsDraggingImage] = useState(false);
	// Use a consistent initial value for server and client to avoid hydration errors
	const [canvasSize, setCanvasSize] = useState({
		width: 1200,
		height: 800,
	});
	const [isCanvasReady, setIsCanvasReady] = useState(false);
	const [isPanningCanvas, setIsPanningCanvas] = useState(false);
	const [lastPanPosition, setLastPanPosition] = useState({ x: 0, y: 0 });
	const [croppingImageId, setCroppingImageId] = useState<string | null>(null);
	const [viewport, setViewport] = useState({
		x: 0,
		y: 0,
		scale: 1,
	});
	const stageRef = useRef<Konva.Stage>(null);
	const [_, setIsSaving] = useState(false);
	const [showSuccess, setShowSuccess] = useState(false);

	// Touch event states for mobile
	const [lastTouchDistance, setLastTouchDistance] = useState<number | null>(null);
	const [lastTouchCenter, setLastTouchCenter] = useState<{
		x: number;
		y: number;
	} | null>(null);
	const [isTouchingImage, setIsTouchingImage] = useState(false);

	// Track when generation completes
	const [previousGenerationCount, setPreviousGenerationCount] = useState(0);

	// Store zoom functions from ZoomControls
	const [zoomFunctions, setZoomFunctions] = useState<{
		handleZoomIn: () => void;
		handleZoomOut: () => void;
		handleZoomToFit: () => void;
	} | null>(null);

	// User menu component
	const userMenu = () => {
		return (
			<DropdownMenu modal={false}>
				<DropdownMenuTrigger asChild className="cursor-pointer">
					<div className="bg-input flex h-8 w-8 shrink-0 flex-row items-center justify-center gap-2 rounded-full">
						<Avatar className="size-8">
							<AvatarImage src={session?.user.image!} alt="User Avatar" />
							<AvatarFallback>{session?.user.name}</AvatarFallback>
						</Avatar>
					</div>
				</DropdownMenuTrigger>
				<DropdownMenuContent className="w-[240px] rounded-2xl border-none p-0" align="end" forceMount>
					<div className="flex gap-5 p-4">
						<Avatar className="flex size-9 shrink-0 items-center gap-2">
							<AvatarImage src={session?.user.image!} alt="User Avatar" />
							<AvatarFallback>{session?.user.name}</AvatarFallback>
						</Avatar>
						<div className="flex min-w-0 flex-1 flex-col items-start">
							<p className="truncate text-sm font-semibold">{session?.user.name ?? WEBNAME}</p>
							<p className="text-muted-foreground mt-1 truncate text-xs">{session?.user.email ?? "--"}</p>
						</div>
					</div>
					{user?.membershipId === MembershipID.Free && (
						<div className="px-[16px] pb-4">
							<Button size="sm" className="w-full bg-blue-500 hover:bg-blue-500/80" onClick={() => setPlanBoxOpen(true)}>
								Get a plan
							</Button>
						</div>
					)}
					<Separator className="bg-muted" />

					<div className="space-y-1 px-1 py-2">
						<Link href="/assets" className={cn(buttonVariants({ variant: "ghost" }), "w-full justify-between px-3 text-xs font-normal")}>
							<div className="flex flex-row items-center">
								<FolderIcon className="mr-3 h-4 w-4 shrink-0" />
								My Assets
							</div>
						</Link>
						<Link href="/user/my-billing" className={cn(buttonVariants({ variant: "ghost" }), "w-full justify-between px-3 text-xs font-normal")}>
							<div className="flex flex-row items-center">
								<CreditCard className="mr-3 h-4 w-4 shrink-0" />
								My Billing
							</div>
							<p className="flex items-center gap-1 rounded bg-zinc-300 px-2 py-0.5 text-[10px] font-medium">{user?.membershipFormatted}</p>
						</Link>
						<a
							href={`mailto:${EMAIL_CONTACT}`}
							className={cn(buttonVariants({ variant: "ghost" }), "w-full justify-between px-3 text-xs font-normal")}
						>
							<div className="flex flex-row items-center">
								<MailIcon className="mr-3 h-4 w-4 shrink-0" />
								Contact Us
							</div>
						</a>
					</div>

					<Separator className="bg-muted" />

					<div className="space-y-1 px-1 py-2">
						<button className={cn(buttonVariants({ variant: "ghost" }), "w-full justify-between px-3 text-xs font-normal")} onClick={handleSignOut}>
							<div className="flex flex-row items-center">
								<LogOutIcon className="mr-3 h-4 w-4 shrink-0" />
								Sign out
							</div>
						</button>
					</div>
				</DropdownMenuContent>
			</DropdownMenu>
		);
	};

	useEffect(() => {
		const currentCount = activeGenerations.size + (isGenerating ? 1 : 0);

		// If we went from having generations to having none, show success
		if (previousGenerationCount > 0 && currentCount === 0) {
			setShowSuccess(true);
			// Hide success after 2 seconds
			const timeout = setTimeout(() => {
				setShowSuccess(false);
			}, 2000);
			return () => clearTimeout(timeout);
		}

		setPreviousGenerationCount(currentCount);
	}, [activeGenerations.size, isGenerating, previousGenerationCount]);

	// Create FAL client instance with proxy
	const falClient: any = 1;

	// Direct FAL upload function using proxy

	const { mutateAsync: removeBackground } = useMutation(orpc.image.removeBackground.mutationOptions());

	const { mutateAsync: generateTextToImage } = useMutation(orpc.image.generateTextToImage.mutationOptions());

	// Save current state to storage
	const saveToStorage = useCallback(async () => {
		try {
			setIsSaving(true);

			// 只保存画布状态，图片现在使用OSS
			const canvasState: CanvasState = {
				elements: [...images.map(imageToCanvasElement)],
				// backgroundColor: "#ffffff",
				lastModified: Date.now(),
				viewport: viewport,
			};
			canvasStorage.saveCanvasState(canvasState);

			// Brief delay to show the indicator
			setTimeout(() => setIsSaving(false), 300);
		} catch (error) {
			console.error("Failed to save to storage:", error);
			setIsSaving(false);
		}
	}, [images, viewport]);

	// Unified history API
	const applyWithHistory = useCallback(
		(updateFn: (prev: PlacedImage[]) => PlacedImage[], actionName: string) => {
			update(updateFn, { commit: true, label: actionName });
		},
		[update],
	);

	// After storage load, set current images as the baseline so initial Undo won't clear everything
	// useEffect(() => {
	// 	if (historyLength === 1 && isStorageLoaded) {
	// 		reset(images);
	// 	}
	// }, [isStorageLoaded, historyLength, images, reset]);

	// Set canvas ready state after mount
	useEffect(() => {
		// Only set canvas ready after we have valid dimensions
		if (canvasSize.width > 0 && canvasSize.height > 0) {
			setIsCanvasReady(true);
		}
	}, [canvasSize]);

	// Update canvas size on window resize
	useEffect(() => {
		const updateCanvasSize = () => {
			setCanvasSize({
				width: window.innerWidth,
				height: window.innerHeight,
			});
		};

		// Set initial size
		updateCanvasSize();

		// Update on resize
		window.addEventListener("resize", updateCanvasSize);
		return () => window.removeEventListener("resize", updateCanvasSize);
	}, []);

	// Prevent body scrolling on mobile
	useEffect(() => {
		// Prevent scrolling on mobile
		document.body.style.overflow = "hidden";
		document.body.style.position = "fixed";
		document.body.style.width = "100%";
		document.body.style.height = "100%";

		return () => {
			document.body.style.overflow = "";
			document.body.style.position = "";
			document.body.style.width = "";
			document.body.style.height = "";
		};
	}, []);

	// Load state from storage (支持新的OSS URL格式)
	const loadFromStorage = useCallback(async () => {
		try {
			const canvasState = canvasStorage.getCanvasState();
			if (!canvasState) {
				setIsStorageLoaded(true);
				return;
			}

			const loadedImages: PlacedImage[] = [];

			for (const element of canvasState.elements) {
				if (element.type === "image" && element.ossUrl) {
					loadedImages.push({
						id: element.id,
						src: element.ossUrl,
						x: element.transform.x,
						y: element.transform.y,
						width: element.width || 300,
						height: element.height || 300,
						rotation: element.transform.rotation,
						status: element.status,
						ossUrl: element.ossUrl,
						...(element.transform.cropBox && {
							cropX: element.transform.cropBox.x,
							cropY: element.transform.cropBox.y,
							cropWidth: element.transform.cropBox.width,
							cropHeight: element.transform.cropBox.height,
						}),
					});
				}
			}

			// Set loaded images
			if (loadedImages.length > 0) {
				setImages(loadedImages);
			}

			// Restore viewport if available
			if (canvasState.viewport) {
				setViewport(canvasState.viewport);
			}

			// Set loaded images as the baseline for history
			reset(loadedImages);
		} catch (error) {
			console.error("Failed to load from storage:", error);
			toast.error("Failed to restore canvas", {
				description: "Starting with a fresh canvas",
			});
		} finally {
			setIsStorageLoaded(true);
		}
	}, []);

	// Load from storage on mount
	useEffect(() => {
		loadFromStorage();
	}, [loadFromStorage]);

	// Auto-save to storage when images change (with debounce)
	useEffect(() => {
		if (!isStorageLoaded) return; // Don't save until we've loaded
		if (activeGenerations.size > 0) return;

		// 检查是否有图片正在上传
		const hasUploadingImages = images.some((img) => img.status === "uploading" || img.status === "generating");
		if (hasUploadingImages) return;

		const timeoutId = setTimeout(() => {
			saveToStorage();
		}, 1000); // Save after 1 second of no changes

		return () => clearTimeout(timeoutId);
	}, [images, viewport, isStorageLoaded, saveToStorage, activeGenerations.size]);

	// Helper function to create a cropped image
	const createCroppedImage = async (imageSrc: string, cropX: number, cropY: number, cropWidth: number, cropHeight: number): Promise<string> => {
		return new Promise((resolve, reject) => {
			const img = new window.Image();
			img.crossOrigin = "anonymous"; // Enable CORS
			img.onload = () => {
				const canvas = document.createElement("canvas");
				const ctx = canvas.getContext("2d");
				if (!ctx) {
					reject(new Error("Failed to get canvas context"));
					return;
				}

				// Set canvas size to the natural cropped dimensions
				canvas.width = cropWidth * img.naturalWidth;
				canvas.height = cropHeight * img.naturalHeight;

				// Draw the cropped portion at full resolution
				ctx.drawImage(
					img,
					cropX * img.naturalWidth,
					cropY * img.naturalHeight,
					cropWidth * img.naturalWidth,
					cropHeight * img.naturalHeight,
					0,
					0,
					canvas.width,
					canvas.height,
				);

				// Convert to data URL
				canvas.toBlob((blob) => {
					if (!blob) {
						reject(new Error("Failed to create blob"));
						return;
					}
					const reader = new FileReader();
					reader.onload = () => resolve(reader.result as string);
					reader.onerror = reject;
					reader.readAsDataURL(blob);
				}, "image/png");
			};
			img.onerror = () => reject(new Error("Failed to load image"));
			img.src = imageSrc;
		});
	};

	// Handle file upload with optimistic UI update
	const handleFileUpload = async (files: FileList | null, position?: { x: number; y: number }) => {
		if (!files) return;

		const fileArray = Array.from(files).filter((file) => file.type.startsWith("image/"));
		if (fileArray.length === 0) return;

		// Process all files and collect the new images
		const newImages: PlacedImage[] = [];

		for (let index = 0; index < fileArray.length; index++) {
			const file = fileArray[index];

			try {
				// Read file as data URL
				const dataUrl = await new Promise<string>((resolve, reject) => {
					const reader = new FileReader();
					reader.onload = (e) => resolve(e.target?.result as string);
					reader.onerror = reject;
					reader.readAsDataURL(file);
				});

				// Load image to get dimensions
				const { width, height } = await new Promise<{ width: number; height: number }>((resolve, reject) => {
					const img = new window.Image();
					img.crossOrigin = "anonymous";
					img.onload = () => resolve({ width: img.width, height: img.height });
					img.onerror = reject;
					img.src = dataUrl;
				});

				// Calculate position
				let x, y;
				if (position) {
					// Convert screen position to canvas coordinates
					x = (position.x - viewport.x) / viewport.scale - width / 2;
					y = (position.y - viewport.y) / viewport.scale - height / 2;
				} else {
					// Center of viewport
					const viewportCenterX = (canvasSize.width / 2 - viewport.x) / viewport.scale;
					const viewportCenterY = (canvasSize.height / 2 - viewport.y) / viewport.scale;
					x = viewportCenterX - width / 2;
					y = viewportCenterY - height / 2;
				}

				// Add offset for multiple files
				if (index > 0) {
					x += index * 20;
					y += index * 20;
				}

				const id = `img-${Date.now()}-${Math.random()}-${index}`;
				const newImage: PlacedImage = {
					id,
					src: dataUrl,
					x,
					y,
					width,
					height,
					rotation: 0,
					status: "uploading",
				};

				newImages.push(newImage);

				// Start uploading to OSS in background
				uploadFile(file, true)
					.then(({ file_url }) => {
						setImages((prev) =>
							prev.map((img) =>
								img.id === id
									? {
											...img,
											status: "completed",
											ossUrl: file_url,
										}
									: img,
							),
						);
						toast.success("Image uploaded successfully");
					})
					.catch((error) => {
						console.error("Failed to upload image:", error);
						setImages((prev) => prev.filter((img) => img.id !== id));
						toast.error("Upload failed", {
							description: error instanceof Error ? error.message : "Failed to upload image",
						});
					});
			} catch (error) {
				console.error("Failed to process file:", error);
				toast.error("Failed to process image", {
					description: error instanceof Error ? error.message : "Unknown error",
				});
			}
		}

		// Add all new images at once and save to history
		if (newImages.length > 0) {
			applyWithHistory((prev) => [...prev, ...newImages], "upload");
		}
	};

	const handleDrop = (e: React.DragEvent) => {
		e.preventDefault();

		// Get drop position relative to the stage
		const stage = stageRef.current;
		if (stage) {
			const container = stage.container();
			const rect = container.getBoundingClientRect();
			const dropPosition = {
				x: e.clientX - rect.left,
				y: e.clientY - rect.top,
			};
			handleFileUpload(e.dataTransfer.files, dropPosition);
		} else {
			handleFileUpload(e.dataTransfer.files);
		}
	};

	// Handle wheel for zoom 鼠标滚动移动画布
	const handleWheel = (e: Konva.KonvaEventObject<WheelEvent>) => {
		e.evt.preventDefault();

		const stage = stageRef.current;
		if (!stage) return;

		// Check if this is a pinch gesture (command key on macOS, ctrl key on other systems)
		const isMac = checkOS("Mac");
		const isZoomGesture = isMac ? e.evt.metaKey : e.evt.ctrlKey;

		if (isZoomGesture) {
			// This is a pinch-to-zoom gesture
			const oldScale = viewport.scale;
			const pointer = stage.getPointerPosition();
			if (!pointer) return;

			const mousePointTo = {
				x: (pointer.x - viewport.x) / oldScale,
				y: (pointer.y - viewport.y) / oldScale,
			};

			// Zoom based on deltaY (negative = zoom in, positive = zoom out)
			const scaleBy = 1.01;
			const direction = e.evt.deltaY > 0 ? -1 : 1;
			const steps = Math.min(Math.abs(e.evt.deltaY), 10);
			let newScale = oldScale;

			for (let i = 0; i < steps; i++) {
				newScale = direction > 0 ? newScale * scaleBy : newScale / scaleBy;
			}

			// Limit zoom (10% to 500%)
			const scale = Math.max(0.1, Math.min(5, newScale));

			const newPos = {
				x: pointer.x - mousePointTo.x * scale,
				y: pointer.y - mousePointTo.y * scale,
			};

			setViewport({ x: newPos.x, y: newPos.y, scale });
		} else {
			// This is a pan gesture (two-finger swipe on trackpad or mouse wheel)
			const deltaX = e.evt.shiftKey ? e.evt.deltaY : e.evt.deltaX;
			const deltaY = e.evt.shiftKey ? 0 : e.evt.deltaY;

			// Invert the direction to match natural scrolling
			setViewport((prev) => ({
				...prev,
				x: prev.x - deltaX,
				y: prev.y - deltaY,
			}));
		}
	};

	// Touch event handlers for mobile 移动端手指移动画布
	const handleTouchStart = (e: Konva.KonvaEventObject<TouchEvent>) => {
		const touches = e.evt.touches;
		const stage = stageRef.current;

		if (touches.length === 2) {
			// Two fingers - prepare for pinch-to-zoom
			const touch1 = { x: touches[0].clientX, y: touches[0].clientY };
			const touch2 = { x: touches[1].clientX, y: touches[1].clientY };

			const distance = Math.sqrt(Math.pow(touch2.x - touch1.x, 2) + Math.pow(touch2.y - touch1.y, 2));

			const center = {
				x: (touch1.x + touch2.x) / 2,
				y: (touch1.y + touch2.y) / 2,
			};

			setLastTouchDistance(distance);
			setLastTouchCenter(center);
		} else if (touches.length === 1) {
			// Single finger - check if touching an image
			const touch = { x: touches[0].clientX, y: touches[0].clientY };

			// Check if we're touching an image
			if (stage) {
				const pos = stage.getPointerPosition();
				if (pos) {
					const canvasPos = {
						x: (pos.x - viewport.x) / viewport.scale,
						y: (pos.y - viewport.y) / viewport.scale,
					};

					// Check if touch is on any image
					const touchedImage = images.some((img) => {
						return canvasPos.x >= img.x && canvasPos.x <= img.x + img.width && canvasPos.y >= img.y && canvasPos.y <= img.y + img.height;
					});

					setIsTouchingImage(touchedImage);
				}
			}

			setLastTouchCenter(touch);
		}
	};
	// 移动端手指移动画布
	const handleTouchMove = (e: Konva.KonvaEventObject<TouchEvent>) => {
		const touches = e.evt.touches;

		if (touches.length === 2 && lastTouchDistance && lastTouchCenter) {
			// Two fingers - handle pinch-to-zoom
			e.evt.preventDefault();

			const touch1 = { x: touches[0].clientX, y: touches[0].clientY };
			const touch2 = { x: touches[1].clientX, y: touches[1].clientY };

			const distance = Math.sqrt(Math.pow(touch2.x - touch1.x, 2) + Math.pow(touch2.y - touch1.y, 2));

			const center = {
				x: (touch1.x + touch2.x) / 2,
				y: (touch1.y + touch2.y) / 2,
			};

			// Calculate scale change
			const scaleFactor = distance / lastTouchDistance;
			const newScale = Math.max(0.1, Math.min(5, viewport.scale * scaleFactor));

			// Calculate new position to zoom towards pinch center
			const stage = stageRef.current;
			if (stage) {
				const stageBox = stage.container().getBoundingClientRect();
				const stageCenter = {
					x: center.x - stageBox.left,
					y: center.y - stageBox.top,
				};

				const mousePointTo = {
					x: (stageCenter.x - viewport.x) / viewport.scale,
					y: (stageCenter.y - viewport.y) / viewport.scale,
				};

				const newPos = {
					x: stageCenter.x - mousePointTo.x * newScale,
					y: stageCenter.y - mousePointTo.y * newScale,
				};

				setViewport({ x: newPos.x, y: newPos.y, scale: newScale });
			}

			setLastTouchDistance(distance);
			setLastTouchCenter(center);
		} else if (touches.length === 1 && lastTouchCenter && !isSelecting && !isDraggingImage && !isTouchingImage) {
			// Single finger - handle pan (only if not selecting, dragging, or touching an image)
			// Don't prevent default if there might be system dialogs open
			const hasActiveFileInput = document.querySelector('input[type="file"]');
			if (!hasActiveFileInput) {
				e.evt.preventDefault();
			}

			const touch = { x: touches[0].clientX, y: touches[0].clientY };
			const deltaX = touch.x - lastTouchCenter.x;
			const deltaY = touch.y - lastTouchCenter.y;

			setViewport((prev) => ({
				...prev,
				x: prev.x + deltaX,
				y: prev.y + deltaY,
			}));

			setLastTouchCenter(touch);
		}
	};
	// 移动端手指移动画布
	const handleTouchEnd = (e: Konva.KonvaEventObject<TouchEvent>) => {
		setLastTouchDistance(null);
		setLastTouchCenter(null);
		setIsTouchingImage(false);
	};

	// Handle selection
	const handleSelect = (id: string, e: Konva.KonvaEventObject<MouseEvent>) => {
		if (e.evt.shiftKey || e.evt.metaKey || e.evt.ctrlKey) {
			setSelectedIds((prev) => (prev.includes(id) ? prev.filter((i) => i !== id) : [...prev, id]));
		} else {
			setSelectedIds([id]);
		}
	};

	// Handle drag selection and panning
	const handleMouseDown = (e: Konva.KonvaEventObject<MouseEvent>) => {
		const clickedOnEmpty = e.target === e.target.getStage();
		const stage = e.target.getStage();
		const mouseButton = e.evt.button; // 0 = left, 1 = middle, 2 = right

		// If middle mouse button, start panning
		if (mouseButton === 1) {
			e.evt.preventDefault();
			setIsPanningCanvas(true);
			setLastPanPosition({ x: e.evt.clientX, y: e.evt.clientY });
			return;
		}

		// If in crop mode and clicked outside, exit crop mode
		if (croppingImageId) {
			const clickedNode = e.target;
			const cropGroup = clickedNode.findAncestor((node: any) => {
				return node.attrs && node.attrs.name === "crop-overlay";
			});

			if (!cropGroup) {
				setCroppingImageId(null);
				return;
			}
		}

		// Start selection box when left-clicking on empty space
		if (clickedOnEmpty && !croppingImageId && mouseButton === 0) {
			const pos = stage?.getPointerPosition();
			if (pos) {
				// Convert screen coordinates to canvas coordinates
				const canvasPos = {
					x: (pos.x - viewport.x) / viewport.scale,
					y: (pos.y - viewport.y) / viewport.scale,
				};

				setIsSelecting(true);
				setSelectionBox({
					startX: canvasPos.x,
					startY: canvasPos.y,
					endX: canvasPos.x,
					endY: canvasPos.y,
					visible: true,
				});
				setSelectedIds([]);
			}
		}
	};

	const handleMouseMove = (e: Konva.KonvaEventObject<MouseEvent>) => {
		const stage = e.target.getStage();

		// Handle canvas panning with middle mouse
		if (isPanningCanvas) {
			const deltaX = e.evt.clientX - lastPanPosition.x;
			const deltaY = e.evt.clientY - lastPanPosition.y;

			setViewport((prev) => ({
				...prev,
				x: prev.x + deltaX,
				y: prev.y + deltaY,
			}));

			setLastPanPosition({ x: e.evt.clientX, y: e.evt.clientY });
			return;
		}

		// Handle selection
		if (!isSelecting) return;

		const pos = stage?.getPointerPosition();
		if (pos) {
			// Convert screen coordinates to canvas coordinates
			const canvasPos = {
				x: (pos.x - viewport.x) / viewport.scale,
				y: (pos.y - viewport.y) / viewport.scale,
			};

			setSelectionBox((prev) => ({
				...prev,
				endX: canvasPos.x,
				endY: canvasPos.y,
			}));
		}
	};

	const handleMouseUp = (e: Konva.KonvaEventObject<MouseEvent>) => {
		// Stop canvas panning
		if (isPanningCanvas) {
			setIsPanningCanvas(false);
			return;
		}

		if (!isSelecting) return;

		// Calculate which images and videos are in the selection box
		const box = {
			x: Math.min(selectionBox.startX, selectionBox.endX),
			y: Math.min(selectionBox.startY, selectionBox.endY),
			width: Math.abs(selectionBox.endX - selectionBox.startX),
			height: Math.abs(selectionBox.endY - selectionBox.startY),
		};

		// Only select if the box has some size
		if (box.width > 5 || box.height > 5) {
			// Check for images in the selection box
			const selectedImages = images.filter((img) => {
				// Check if image intersects with selection box
				return !(img.x + img.width < box.x || img.x > box.x + box.width || img.y + img.height < box.y || img.y > box.y + box.height);
			});

			// Combine selected images and videos
			const selectedIds = [...selectedImages.map((img) => img.id)];

			if (selectedIds.length > 0) {
				setSelectedIds(selectedIds);
			}
		}

		setIsSelecting(false);
		setSelectionBox({ ...selectionBox, visible: false });
	};

	// Handle context menu actions
	const handleRun = async () => {
		await handleRunHandler({
			images,
			selectedIds,
			generationSettings,
			canvasSize,
			viewport,
			falClient,
			setImages,
			setSelectedIds,
			setActiveGenerations,
			setIsGenerating,
			generateTextToImage,
			applyWithHistory,
		});
	};

	const handleDelete = () => {
		applyWithHistory((prev) => prev.filter((img) => !selectedIds.includes(img.id)), "delete");
		setSelectedIds([]);
	};

	const handleDuplicate = () => {
		// Duplicate selected images
		const selectedImages = images.filter((img) => selectedIds.includes(img.id));
		const newImages = selectedImages.map((img) => ({
			...img,
			id: `img-${Date.now()}-${Math.random()}`,
			x: img.x + 20,
			y: img.y + 20,
		}));

		// Update both arrays
		applyWithHistory((prev) => [...prev, ...newImages], "duplicate");

		// Select all duplicated items
		const newIds = [...newImages.map((img) => img.id)];
		setSelectedIds(newIds);
	};

	const handleRemoveBackground = async () => {
		await handleRemoveBackgroundHandler({
			images,
			selectedIds,
			setImages,
			applyWithHistory,
			removeBackground,
			falClient,
		});
	};

	const sendToFront = useCallback(() => {
		if (selectedIds.length === 0) return;

		applyWithHistory((prev) => {
			// Get selected images in their current order
			const selectedImages = selectedIds.map((id) => prev.find((img) => img.id === id)).filter(Boolean) as PlacedImage[];

			// Get remaining images
			const remainingImages = prev.filter((img) => !selectedIds.includes(img.id));

			// Place selected images at the end (top layer)
			return [...remainingImages, ...selectedImages];
		}, "sendToFront");
	}, [selectedIds, applyWithHistory]);

	const sendToBack = useCallback(() => {
		if (selectedIds.length === 0) return;

		applyWithHistory((prev) => {
			// Get selected images in their current order
			const selectedImages = selectedIds.map((id) => prev.find((img) => img.id === id)).filter(Boolean) as PlacedImage[];

			// Get remaining images
			const remainingImages = prev.filter((img) => !selectedIds.includes(img.id));

			// Place selected images at the beginning (bottom layer)
			return [...selectedImages, ...remainingImages];
		}, "sendToBack");
	}, [selectedIds, applyWithHistory]);

	const bringForward = useCallback(() => {
		if (selectedIds.length === 0) return;

		applyWithHistory((prev) => {
			const result = [...prev];

			// Process selected images from back to front to maintain relative order
			for (let i = result.length - 2; i >= 0; i--) {
				if (selectedIds.includes(result[i].id) && !selectedIds.includes(result[i + 1].id)) {
					// Swap with the next image if it's not also selected
					[result[i], result[i + 1]] = [result[i + 1], result[i]];
				}
			}

			return result;
		}, "bringForward");
	}, [selectedIds, applyWithHistory]);

	const sendBackward = useCallback(() => {
		if (selectedIds.length === 0) return;

		applyWithHistory((prev) => {
			const result = [...prev];

			// Process selected images from front to back to maintain relative order
			for (let i = 1; i < result.length; i++) {
				if (selectedIds.includes(result[i].id) && !selectedIds.includes(result[i - 1].id)) {
					// Swap with the previous image if it's not also selected
					[result[i], result[i - 1]] = [result[i - 1], result[i]];
				}
			}

			return result;
		}, "sendBackward");
	}, [selectedIds, applyWithHistory]);

	const handleCombineImages = async () => {
		if (selectedIds.length < 2) return;

		// Get selected images and sort by layer order
		const selectedImages = selectedIds.map((id) => images.find((img) => img.id === id)).filter((img) => img !== undefined) as PlacedImage[];

		const sortedImages = [...selectedImages].sort((a, b) => {
			const indexA = images.findIndex((img) => img.id === a.id);
			const indexB = images.findIndex((img) => img.id === b.id);
			return indexA - indexB;
		});

		// Load all images to calculate scale factors
		const imageElements: {
			img: PlacedImage;
			element: HTMLImageElement;
			scale: number;
		}[] = [];
		let maxScale = 1;

		for (const img of sortedImages) {
			const imgElement = new window.Image();
			imgElement.crossOrigin = "anonymous"; // Enable CORS
			imgElement.src = img.src;
			await new Promise((resolve) => {
				imgElement.onload = resolve;
			});

			// Calculate scale factor (original size / display size)
			// Account for crops if they exist
			const effectiveWidth = img.cropWidth ? imgElement.naturalWidth * img.cropWidth : imgElement.naturalWidth;
			const effectiveHeight = img.cropHeight ? imgElement.naturalHeight * img.cropHeight : imgElement.naturalHeight;

			const scaleX = effectiveWidth / img.width;
			const scaleY = effectiveHeight / img.height;
			const scale = Math.min(scaleX, scaleY); // Use min to maintain aspect ratio

			maxScale = Math.max(maxScale, scale);
			imageElements.push({ img, element: imgElement, scale });
		}

		// Use a reasonable scale - not too large to avoid memory issues
		const optimalScale = Math.min(maxScale, 4); // Cap at 4x to prevent huge images

		// Calculate bounding box of all selected images
		let minX = Infinity,
			minY = Infinity;
		let maxX = -Infinity,
			maxY = -Infinity;

		sortedImages.forEach((img) => {
			minX = Math.min(minX, img.x);
			minY = Math.min(minY, img.y);
			maxX = Math.max(maxX, img.x + img.width);
			maxY = Math.max(maxY, img.y + img.height);
		});

		const combinedWidth = maxX - minX;
		const combinedHeight = maxY - minY;

		// Create canvas at higher resolution
		const canvas = document.createElement("canvas");
		const ctx = canvas.getContext("2d");
		if (!ctx) {
			console.error("Failed to get canvas context");
			return;
		}

		canvas.width = Math.round(combinedWidth * optimalScale);
		canvas.height = Math.round(combinedHeight * optimalScale);

		console.log(`Creating combined image at ${canvas.width}x${canvas.height} (scale: ${optimalScale.toFixed(2)}x)`);

		// Draw each image in order using the pre-loaded elements
		for (const { img, element: imgElement } of imageElements) {
			// Calculate position relative to the combined canvas, scaled up
			const relX = (img.x - minX) * optimalScale;
			const relY = (img.y - minY) * optimalScale;
			const scaledWidth = img.width * optimalScale;
			const scaledHeight = img.height * optimalScale;

			ctx.save();

			// Handle rotation if needed
			if (img.rotation) {
				ctx.translate(relX + scaledWidth / 2, relY + scaledHeight / 2);
				ctx.rotate((img.rotation * Math.PI) / 180);
				ctx.drawImage(imgElement, -scaledWidth / 2, -scaledHeight / 2, scaledWidth, scaledHeight);
			} else {
				// Handle cropping if exists
				if (img.cropX !== undefined && img.cropY !== undefined && img.cropWidth !== undefined && img.cropHeight !== undefined) {
					ctx.drawImage(
						imgElement,
						img.cropX * imgElement.naturalWidth,
						img.cropY * imgElement.naturalHeight,
						img.cropWidth * imgElement.naturalWidth,
						img.cropHeight * imgElement.naturalHeight,
						relX,
						relY,
						scaledWidth,
						scaledHeight,
					);
				} else {
					ctx.drawImage(imgElement, 0, 0, imgElement.naturalWidth, imgElement.naturalHeight, relX, relY, scaledWidth, scaledHeight);
				}
			}

			ctx.restore();
		}

		// Convert to blob and create data URL
		const blob = await new Promise<Blob>((resolve) => {
			canvas.toBlob((blob) => resolve(blob!), "image/png");
		});

		const reader = new FileReader();
		const dataUrl = await new Promise<string>((resolve) => {
			reader.onload = (e) => resolve(e.target?.result as string);
			reader.readAsDataURL(blob);
		});

		// Create new combined image
		const combinedImage: PlacedImage = {
			id: `combined-${Date.now()}-${Math.random()}`,
			src: dataUrl,
			x: minX,
			y: minY,
			width: combinedWidth,
			height: combinedHeight,
			rotation: 0,
		};

		// Remove the original images and add the combined one
		applyWithHistory((prev) => [...prev.filter((img) => !selectedIds.includes(img.id)), combinedImage], "combine");

		// Select the new combined image
		setSelectedIds([combinedImage.id]);
	};

	// Handle keyboard shortcuts
	useEffect(() => {
		const handleKeyDown = (e: KeyboardEvent) => {
			// Check if target is an input element
			const isInputElement = e.target && (e.target as HTMLElement).matches("input, textarea");

			// Check OS for proper modifier key
			const isMac = checkOS("Mac");
			const modifierKey = isMac ? e.metaKey : e.ctrlKey;

			// Undo/Redo
			if (modifierKey && e.key === "z" && !e.shiftKey) {
				e.preventDefault();
				undo();
			} else if (modifierKey && ((e.key === "z" && e.shiftKey) || e.key === "y")) {
				e.preventDefault();
				redo();
			}
			// Select all
			else if (modifierKey && e.key === "a" && !isInputElement) {
				e.preventDefault();
				setSelectedIds(images.map((img) => img.id));
			}
			// Delete
			else if ((e.key === "Delete" || e.key === "Backspace") && !isInputElement) {
				if (selectedIds.length > 0) {
					e.preventDefault();
					handleDelete();
				}
			}
			// Duplicate
			else if (modifierKey && e.key === "d" && !isInputElement) {
				e.preventDefault();
				if (selectedIds.length > 0) {
					handleDuplicate();
				}
			}
			// Run generation
			// else if (modifierKey && e.key === "Enter" && !isInputElement) {
			// 	e.preventDefault();
			// 	if (!isGenerating && generationSettings.prompt.trim()) {
			// 		handleRun();
			// 	}
			// }
			// Layer ordering shortcuts
			else if (e.key === "]" && !isInputElement) {
				e.preventDefault();
				if (selectedIds.length > 0) {
					if (modifierKey) {
						bringForward();
					} else {
						sendToFront();
					}
				}
			} else if (e.key === "[" && !isInputElement) {
				e.preventDefault();
				if (selectedIds.length > 0) {
					if (modifierKey) {
						sendBackward();
					} else {
						sendToBack();
					}
				}
			}
			// Escape to exit crop mode
			else if (e.key === "Escape" && croppingImageId) {
				e.preventDefault();
				setCroppingImageId(null);
			}
			// Zoom in
			else if ((e.key === "+" || e.key === "=") && !isInputElement) {
				e.preventDefault();
				const newScale = Math.min(5, viewport.scale * 1.2);
				const centerX = canvasSize.width / 2;
				const centerY = canvasSize.height / 2;

				const mousePointTo = {
					x: (centerX - viewport.x) / viewport.scale,
					y: (centerY - viewport.y) / viewport.scale,
				};

				setViewport({
					x: centerX - mousePointTo.x * newScale,
					y: centerY - mousePointTo.y * newScale,
					scale: newScale,
				});
			}
			// Zoom out
			else if (e.key === "-" && !isInputElement) {
				e.preventDefault();
				const newScale = Math.max(0.1, viewport.scale / 1.2);
				const centerX = canvasSize.width / 2;
				const centerY = canvasSize.height / 2;

				const mousePointTo = {
					x: (centerX - viewport.x) / viewport.scale,
					y: (centerY - viewport.y) / viewport.scale,
				};

				setViewport({
					x: centerX - mousePointTo.x * newScale,
					y: centerY - mousePointTo.y * newScale,
					scale: newScale,
				});
			}
			// Reset zoom
			else if (e.key === "0" && modifierKey) {
				e.preventDefault();
				setViewport({ x: 0, y: 0, scale: 1 });
			}
			// Zoom to fit
			else if ((e.key === "!" || e.key === "！") && !isInputElement) {
				e.preventDefault();
				if (zoomFunctions) {
					zoomFunctions.handleZoomToFit();
				}
			}
		};

		const handleKeyUp = (_e: KeyboardEvent) => {
			// Currently no key up handlers needed
		};

		window.addEventListener("keydown", handleKeyDown);
		window.addEventListener("keyup", handleKeyUp);
		return () => {
			window.removeEventListener("keydown", handleKeyDown);
			window.removeEventListener("keyup", handleKeyUp);
		};
	}, [
		selectedIds,
		images,
		generationSettings,
		undo,
		redo,
		handleDelete,
		handleDuplicate,
		handleRun,
		croppingImageId,
		viewport,
		canvasSize,
		sendToFront,
		sendToBack,
		bringForward,
		sendBackward,
	]);

	return (
		<div
			className="text-foreground font-focal relative flex h-screen w-full flex-col overflow-hidden bg-neutral-100"
			style={{ height: "100dvh" }}
			onDrop={handleDrop}
			onDragOver={(e) => e.preventDefault()}
			onDragEnter={(e) => e.preventDefault()}
			onDragLeave={(e) => e.preventDefault()}
		>
			{/* Render streaming components for active generations */}
			{Array.from(activeGenerations.entries()).map(([imageId, generation]) => (
				<HandleGenerateImage
					key={imageId}
					imageId={imageId}
					generation={generation}
					onComplete={async (id, finalUrl) => {
						// 加载生成的图片以获取实际尺寸
						const img = new window.Image();
						img.crossOrigin = "anonymous";

						img.onload = () => {
							// 使用图片的实际尺寸，不做任何缩放处理
							const width = img.width;
							const height = img.height;

							setImages((prev) =>
								prev.map((imgItem) =>
									imgItem.id === id
										? {
												...imgItem,
												src: finalUrl,
												width,
												height,
												ossUrl: finalUrl,
												status: "completed",
											}
										: imgItem,
								),
							);
						};

						img.onerror = () => {
							// 如果图片加载失败，使用默认尺寸
							setImages((prev) =>
								prev.map((imgItem) =>
									imgItem.id === id
										? {
												...imgItem,
												src: finalUrl,
												ossUrl: finalUrl,
												status: "completed",
											}
										: imgItem,
								),
							);
						};

						img.src = finalUrl;

						setActiveGenerations((prev) => {
							const newMap = new Map(prev);
							newMap.delete(id);
							return newMap;
						});
						setIsGenerating(false);

						// Immediately save after generation completes
						setTimeout(() => {
							saveToStorage();
						}, 100); // Small delay to ensure state updates are processed
					}}
					onError={(id, error) => {
						console.error(`Generation error for ${id}:`, error);
						// Remove the failed image
						setImages((prev) => prev.filter((img) => img.id !== id));
						setActiveGenerations((prev) => {
							const newMap = new Map(prev);
							newMap.delete(id);
							return newMap;
						});
						setIsGenerating(false);
						toast.error("Generation failed", {
							description: error.toString(),
						});
					}}
				/>
			))}

			{/* Main content */}
			<main className="relative flex w-full flex-1 items-center justify-center">
				<div className="relative h-full w-full">
					<ContextMenu>
						<ContextMenuTrigger asChild>
							<div
								className="relative h-full w-full overflow-hidden bg-neutral-100"
								style={{
									// Use consistent style property names to avoid hydration errors
									height: `${canvasSize.height}px`,
									width: `${canvasSize.width}px`,
									minHeight: `${canvasSize.height}px`,
									minWidth: `${canvasSize.width}px`,
									cursor: isPanningCanvas ? "grabbing" : "default",
									WebkitTouchCallout: "none", // Add this for iOS
									touchAction: "none", // For touch devices
								}}
							>
								{isCanvasReady && (
									<Stage
										ref={stageRef}
										width={canvasSize.width}
										height={canvasSize.height}
										x={viewport.x}
										y={viewport.y}
										scaleX={viewport.scale}
										scaleY={viewport.scale}
										draggable={false}
										onDragStart={(e) => {
											e.evt?.preventDefault();
										}}
										onDragEnd={(e) => {
											e.evt?.preventDefault();
										}}
										onMouseDown={handleMouseDown}
										onMouseMove={handleMouseMove}
										onMouseUp={handleMouseUp}
										onMouseLeave={() => {
											// Stop panning if mouse leaves the stage
											if (isPanningCanvas) {
												setIsPanningCanvas(false);
											}
										}}
										onTouchStart={handleTouchStart}
										onTouchMove={handleTouchMove}
										onTouchEnd={handleTouchEnd}
										onContextMenu={(e) => {
											// Check if this is a forwarded event from a video overlay
											const videoId = (e.evt as any)?.videoId || (e as any)?.videoId;
											if (videoId) {
												// This is a right-click on a video
												if (!selectedIds.includes(videoId)) {
													setSelectedIds([videoId]);
												}
												return;
											}

											// Get clicked position
											const stage = e.target.getStage();
											if (!stage) return;

											const point = stage.getPointerPosition();
											if (!point) return;

											// Convert to canvas coordinates
											const canvasPoint = {
												x: (point.x - viewport.x) / viewport.scale,
												y: (point.y - viewport.y) / viewport.scale,
											};

											// Check if we clicked on an image (check in reverse order for top-most image)
											const clickedImage = [...images].reverse().find((img) => {
												// Simple bounding box check
												// TODO: Could be improved to handle rotation
												return (
													canvasPoint.x >= img.x &&
													canvasPoint.x <= img.x + img.width &&
													canvasPoint.y >= img.y &&
													canvasPoint.y <= img.y + img.height
												);
											});

											if (clickedImage) {
												if (!selectedIds.includes(clickedImage.id)) {
													// If clicking on unselected image, select only that image
													setSelectedIds([clickedImage.id]);
												}
												// If already selected, keep current selection for multi-select context menu
											}
										}}
										onWheel={handleWheel}
									>
										<Layer>
											{/* Selection box */}
											<SelectionBoxComponent selectionBox={selectionBox} />

											{/* Multi-selection bounding box */}
											{selectedIds.length > 1 &&
												(() => {
													const selectedImages = images.filter((img) => selectedIds.includes(img.id));
													if (selectedImages.length === 0) return null;

													// Calculate bounding box of all selected images
													let minX = Infinity,
														minY = Infinity;
													let maxX = -Infinity,
														maxY = -Infinity;

													selectedImages.forEach((img) => {
														minX = Math.min(minX, img.x);
														minY = Math.min(minY, img.y);
														maxX = Math.max(maxX, img.x + img.width);
														maxY = Math.max(maxY, img.y + img.height);
													});

													const padding = 4; // Add some padding around the selection
													return (
														<Rect
															x={minX - padding}
															y={minY - padding}
															width={maxX - minX + padding * 2}
															height={maxY - minY + padding * 2}
															stroke="#3b82f6"
															strokeWidth={3}
															dash={[10, 5]}
															fill="transparent"
														/>
													);
												})()}

											{/* Render images */}
											{images
												.filter((image) => {
													// Performance optimization: only render visible images
													const buffer = 100; // pixels buffer
													const viewBounds = {
														left: -viewport.x / viewport.scale - buffer,
														top: -viewport.y / viewport.scale - buffer,
														right: (canvasSize.width - viewport.x) / viewport.scale + buffer,
														bottom: (canvasSize.height - viewport.y) / viewport.scale + buffer,
													};

													return !(
														image.x + image.width < viewBounds.left ||
														image.x > viewBounds.right ||
														image.y + image.height < viewBounds.top ||
														image.y > viewBounds.bottom
													);
												})
												.map((image) => (
													<CanvasImage
														key={image.id}
														image={image}
														isSelected={selectedIds.includes(image.id)}
														onSelect={(e) => handleSelect(image.id, e)}
														onChange={(newAttrs) => {
															setImages((prev) =>
																prev.map((img) =>
																	img.id === image.id
																		? {
																				...img,
																				...newAttrs,
																			}
																		: img,
																),
															);
														}}
														onDoubleClick={() => {
															setCroppingImageId(image.id);
														}}
														onDragStart={() => {
															// If dragging a selected item in a multi-selection, keep the selection
															// If dragging an unselected item, select only that item
															let currentSelectedIds = selectedIds;
															if (!selectedIds.includes(image.id)) {
																currentSelectedIds = [image.id];
																setSelectedIds(currentSelectedIds);
															}

															setIsDraggingImage(true);
															// Save positions of all selected items for drag delta calculation
															const positions = new Map<
																string,
																{
																	x: number;
																	y: number;
																}
															>();
															currentSelectedIds.forEach((id) => {
																const img = images.find((i) => i.id === id);
																if (img) {
																	positions.set(id, {
																		x: img.x,
																		y: img.y,
																	});
																}
															});
															setDragStartPositions(positions);
														}}
														onDragEnd={() => {
															setIsDraggingImage(false);
															setDragStartPositions(new Map());
															// 拖拽结束后，使用applyWithHistory提交最终状态
															// 这会创建一个新的历史记录条目
															applyWithHistory((prev) => prev, "drag");
														}}
														selectedIds={selectedIds}
														images={images}
														setImages={setImages}
														isDraggingImage={isDraggingImage}
														isCroppingImage={croppingImageId === image.id}
														dragStartPositions={dragStartPositions}
													/>
												))}

											{/* Crop overlay */}
											{croppingImageId &&
												(() => {
													const croppingImage = images.find((img) => img.id === croppingImageId);
													if (!croppingImage) return null;

													return (
														<CropOverlayWrapper
															image={croppingImage}
															viewportScale={viewport.scale}
															onCropChange={(crop) => {
																setImages((prev) =>
																	prev.map((img) =>
																		img.id === croppingImageId
																			? {
																					...img,
																					...crop,
																				}
																			: img,
																	),
																);
															}}
															onCropEnd={async () => {
																// Apply crop to image dimensions
																if (croppingImage) {
																	const cropWidth = croppingImage.cropWidth || 1;
																	const cropHeight = croppingImage.cropHeight || 1;
																	const cropX = croppingImage.cropX || 0;
																	const cropY = croppingImage.cropY || 0;

																	try {
																		// Create the cropped image at full resolution
																		const croppedImageSrc = await createCroppedImage(
																			croppingImage.src,
																			cropX,
																			cropY,
																			cropWidth,
																			cropHeight,
																		);

																		applyWithHistory(
																			(prev) =>
																				prev.map((img) =>
																					img.id === croppingImageId
																						? {
																								...img,
																								// Replace with cropped image
																								src: croppedImageSrc,
																								// Update position to the crop area's top-left
																								x: img.x + cropX * img.width,
																								y: img.y + cropY * img.height,
																								// Update dimensions to match crop size
																								width: cropWidth * img.width,
																								height: cropHeight * img.height,
																								// Remove crop values completely
																								cropX: undefined,
																								cropY: undefined,
																								cropWidth: undefined,
																								cropHeight: undefined,
																							}
																						: img,
																				),
																			"crop",
																		);
																	} catch (error) {
																		console.error("Failed to create cropped image:", error);
																	}
																}

																setCroppingImageId(null);
															}}
														/>
													);
												})()}
										</Layer>
									</Stage>
								)}
							</div>
						</ContextMenuTrigger>
						<CanvasContextMenu
							selectedIds={selectedIds}
							images={images}
							isGenerating={isGenerating}
							generationSettings={generationSettings}
							handleRun={handleRun}
							handleDuplicate={handleDuplicate}
							handleRemoveBackground={handleRemoveBackground}
							handleCombineImages={handleCombineImages}
							handleDelete={handleDelete}
							setCroppingImageId={setCroppingImageId}
							sendToFront={sendToFront}
							sendToBack={sendToBack}
							bringForward={bringForward}
							sendBackward={sendBackward}
							onZoomIn={zoomFunctions?.handleZoomIn}
							onZoomOut={zoomFunctions?.handleZoomOut}
							onZoomToFit={zoomFunctions?.handleZoomToFit}
						/>
					</ContextMenu>

					<div className="absolute top-4 left-4 z-20 flex flex-col items-start gap-2">
						{/* Mobile tool icons - animated based on selection */}
						<MobileToolbar
							selectedIds={selectedIds}
							images={images}
							isGenerating={isGenerating}
							generationSettings={generationSettings}
							handleRun={handleRun}
							handleDuplicate={handleDuplicate}
							handleRemoveBackground={handleRemoveBackground}
							handleCombineImages={handleCombineImages}
							handleDelete={handleDelete}
							setCroppingImageId={setCroppingImageId}
							sendToFront={sendToFront}
							sendToBack={sendToBack}
							bringForward={bringForward}
							sendBackward={sendBackward}
						/>
					</div>

					<div className="fixed right-0 bottom-0 left-0 z-20 p-2 pb-[calc(0.5rem+env(safe-area-inset-bottom))] md:absolute md:bottom-2 md:left-1/2 md:max-w-[648px] md:-translate-x-1/2 md:transform md:p-0 md:pb-0">
						<div className={cn("bg-card/95 rounded-3xl backdrop-blur-lg", "shadow")}>
							<div className="relative flex flex-col gap-3 px-3 py-2 md:px-3 md:py-3">
								{/* Active generations indicator */}
								{/* <AnimatePresence mode="wait">
									{(activeGenerations.size > 0 ||
										activeVideoGenerations.size > 0 ||
										isGenerating ||
										isRemovingVideoBackground ||
										isIsolating ||
										isExtendingVideo ||
										isTransformingVideo ||
										showSuccess) && (
										<motion.div
											key={showSuccess ? "success" : "generating"}
											initial={{
												opacity: 0,
												y: -10,
												scale: 0.9,
												x: "-50%",
											}}
											animate={{
												opacity: 1,
												y: 0,
												scale: 1,
												x: "-50%",
											}}
											exit={{
												opacity: 0,
												y: -10,
												scale: 0.9,
												x: "-50%",
											}}
											transition={{
												duration: 0.2,
												ease: "easeInOut",
											}}
											className={cn(
												"absolute -top-16 left-1/2 z-50",
												"rounded-xl",
												showSuccess
													? "shadow-[0_0_0_1px_rgba(34,197,94,0.2),0_4px_8px_-0.5px_rgba(34,197,94,0.08),0_8px_16px_-2px_rgba(34,197,94,0.04)] dark:border dark:border-green-500/30 dark:shadow-none"
													: activeVideoGenerations.size > 0 || isRemovingVideoBackground || isExtendingVideo || isTransformingVideo
														? "shadow-[0_0_0_1px_rgba(168,85,247,0.2),0_4px_8px_-0.5px_rgba(168,85,247,0.08),0_8px_16px_-2px_rgba(168,85,247,0.04)] dark:border dark:border-purple-500/30 dark:shadow-none"
														: "shadow-[0_0_0_1px_rgba(236,6,72,0.2),0_4px_8px_-0.5px_rgba(236,6,72,0.08),0_8px_16px_-2px_rgba(236,6,72,0.04)] dark:border dark:border-[#EC0648]/30 dark:shadow-none",
											)}
										>
											<GenerationsIndicator
												isAnimating={!showSuccess}
												isSuccess={showSuccess}
												className="h-5 w-5"
												activeGenerationsSize={
													activeGenerations.size +
													activeVideoGenerations.size +
													(isGenerating ? 1 : 0) +
													(isRemovingVideoBackground ? 1 : 0) +
													(isIsolating ? 1 : 0) +
													(isExtendingVideo ? 1 : 0) +
													(isTransformingVideo ? 1 : 0)
												}
												outputType={
													activeVideoGenerations.size > 0 || isRemovingVideoBackground || isExtendingVideo || isTransformingVideo
														? "video"
														: "image"
												}
											/>
										</motion.div>
									)}
								</AnimatePresence> */}

								{/* Action buttons row */}
								<div className="flex items-center gap-1">
									<div className="flex items-center gap-3">
										{/* Mode indicator badge */}
										<div
											className={cn(
												"flex h-9 items-center overflow-clip rounded-xl px-3",
												"pointer-events-none select-none",
												selectedIds.length > 0
													? "bg-blue-500/10 shadow-[0_0_0_1px_rgba(59,130,246,0.2),0_4px_8px_-0.5px_rgba(59,130,246,0.08),0_8px_16px_-2px_rgba(59,130,246,0.04)] dark:border dark:border-blue-500/30 dark:bg-blue-500/15 dark:shadow-none"
													: "bg-orange-500/10 shadow-[0_0_0_1px_rgba(249,115,22,0.2),0_4px_8px_-0.5px_rgba(249,115,22,0.08),0_8px_16px_-2px_rgba(249,115,22,0.04)] dark:border dark:border-orange-500/30 dark:bg-orange-500/15 dark:shadow-none",
											)}
										>
											{selectedIds.length > 0 ? (
												<div className="flex items-center gap-2 text-xs font-medium">
													<ImageIcon className="h-4 w-4 text-blue-600 dark:text-blue-500" />
													<span className="text-blue-600 dark:text-blue-500">Image to Image</span>
												</div>
											) : (
												<div className="flex items-center gap-2 text-xs font-medium">
													<span className="text-sm font-bold text-orange-600 dark:text-orange-500">T</span>
													<span className="text-orange-600 dark:text-orange-500">Text to Image</span>
												</div>
											)}
										</div>
									</div>
									<div className="flex-1" />
								</div>

								{/* Selected images display above textarea */}
								{selectedIds.length > 0 && (
									<div className="">
										<div className="flex gap-2 overflow-x-auto">
											{selectedIds.map((id) => {
												const image = images.find((img) => img.id === id);
												if (!image) return null;

												return (
													<div
														key={id}
														className="border-border/20 bg-background h-16 w-16 flex-shrink-0 overflow-hidden rounded-lg border"
													>
														<img src={image.src} alt="" className="h-full w-full object-cover" />
													</div>
												);
											})}
										</div>
									</div>
								)}

								<div className="relative">
									<Textarea
										value={generationSettings.prompt}
										onChange={(e) =>
											setGenerationSettings({
												...generationSettings,
												prompt: e.target.value,
											})
										}
										placeholder={`Enter a prompt...`}
										className="h-20 w-full resize-none border-none p-2"
										style={{ fontSize: "16px" }}
										onKeyDown={(e) => {
											if ((e.metaKey || e.ctrlKey) && e.key === "Enter") {
												e.preventDefault();
												if (!isGenerating && generationSettings.prompt.trim()) {
													handleRun();
												}
											}
										}}
									/>
								</div>

								{/* Style dropdown and Run button */}
								<div className="flex items-center justify-between">
									<TooltipProvider>
										<Tooltip delayDuration={0}>
											<TooltipTrigger asChild>
												<Button
													size="icon"
													className="rounded-full border-none"
													onClick={() => {
														// Create file input with better mobile support
														const input = document.createElement("input");
														input.type = "file";
														// only support png, jpg, jpeg
														input.accept = "image/png, image/jpeg, image/jpg";
														input.multiple = true;

														// Add to DOM for mobile compatibility
														input.style.position = "fixed";
														input.style.top = "-1000px";
														input.style.left = "-1000px";
														input.style.opacity = "0";
														input.style.pointerEvents = "none";
														input.style.width = "1px";
														input.style.height = "1px";

														// Add event handlers
														input.onchange = (e) => {
															try {
																handleFileUpload((e.target as HTMLInputElement).files);
															} catch (error) {
																console.error("File upload error:", error);
																toast.error("Upload failed", {
																	description: "Failed to process selected files",
																});
															} finally {
																// Clean up
																if (input.parentNode) {
																	document.body.removeChild(input);
																}
															}
														};

														input.onerror = () => {
															console.error("File input error");
															if (input.parentNode) {
																document.body.removeChild(input);
															}
														};

														// Add to DOM and trigger
														document.body.appendChild(input);

														// Use setTimeout to ensure the input is properly attached
														setTimeout(() => {
															try {
																input.click();
															} catch (error) {
																console.error("Failed to trigger file dialog:", error);
																toast.error("Upload unavailable", {
																	description: "File upload is not available. Try using drag & drop instead.",
																});
																if (input.parentNode) {
																	document.body.removeChild(input);
																}
															}
														}, 10);

														// Cleanup after timeout in case dialog was cancelled
														setTimeout(() => {
															if (input.parentNode) {
																document.body.removeChild(input);
															}
														}, 30000); // 30 second cleanup
													}}
													title="Upload images"
												>
													<PlusIcon className="h-4 w-4" />
												</Button>
											</TooltipTrigger>
											<TooltipContent>
												<span>Upload Image</span>
											</TooltipContent>
										</Tooltip>
									</TooltipProvider>

									<div className="flex items-center gap-2">
										<Button
											onClick={handleRun}
											size="icon"
											disabled={isGenerating || !generationSettings.prompt.trim()}
											className={cn("gap-2 font-medium transition-all")}
										>
											{isGenerating ? (
												<LoaderCircleIcon className="h-4 w-4 animate-spin text-white" />
											) : (
												<ZapIcon className="h-4 w-4 fill-white text-white" />
											)}
										</Button>
									</div>
								</div>
							</div>
						</div>
					</div>

					{/* {isSaving && (
            <div className="absolute top-4 left-1/2 transform -translate-x-1/2 z-30 bg-background/95 border rounded-xl px-3 py-2 flex items-center gap-2 shadow-sm">
              <SpinnerIcon className="h-4 w-4 animate-spin text-muted-foreground" />
              <span className="text-sm text-muted-foreground">Saving...</span>
            </div>
          )} */}

					{/* Zoom controls */}
					<ZoomControls
						viewport={viewport}
						setViewport={setViewport}
						canvasSize={canvasSize}
						images={images}
						onZoomFunctionsReady={setZoomFunctions}
					/>

					{/* Header dropdown */}
					<HeaderDropdown
						onZoomIn={zoomFunctions?.handleZoomIn}
						onZoomOut={zoomFunctions?.handleZoomOut}
						onZoomToFit={zoomFunctions?.handleZoomToFit}
						onUndo={undo}
						onRedo={redo}
						canUndo={canUndo}
						canRedo={canRedo}
					/>

					{/* Dimension display for selected images */}
					<DimensionDisplay selectedImages={images.filter((img) => selectedIds.includes(img.id))} viewport={viewport} />

					{/* User credits and avatar menu in top right corner */}
					<div className="absolute top-4 right-4 z-20 flex flex-row items-center gap-2">
						{session ? (
							<>
								{user?.membershipId === MembershipID.Free && (
									<Button
										size="sm"
										className="h-8 cursor-pointer bg-blue-500 text-[13px] font-normal hover:bg-blue-500/80"
										onClick={() => setPlanBoxOpen(true)}
									>
										<GemIcon className="size-3.5" />
										Upgrade
									</Button>
								)}
								<div className="bg-muted hidden h-8 flex-row items-center gap-1 rounded-lg px-3 py-1.5 text-sm text-green-500 md:flex">
									<CoinsIcon className="size-4" /> <span>{userCreditsAll}</span>
								</div>
								<div className="">{userMenu()}</div>
							</>
						) : (
							<Button onClick={() => setSignInBoxOpen(true)} className="bg-foreground hover:bg-foreground/80 rounded-full">
								Sign In
							</Button>
						)}
					</div>
				</div>
			</main>
		</div>
	);
}
